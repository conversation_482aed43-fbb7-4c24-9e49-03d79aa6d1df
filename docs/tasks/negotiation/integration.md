# Match Timeline Integration

## Architectural Overview

### System Architecture Diagram

```mermaid
graph TB
    %% User Layer
    subgraph "User Interface"
        UI[Timeline UI Components]
        UIA[User Actions]
        UIB[Timeline Blocks]
        UIC[Real-time Updates]
    end

    %% Frontend Layer
    subgraph "Frontend Layer"
        CTX[MatchContext]
        HOOK[useMatchTimeline Hook]
        REG[Block Registry]
        TRANS[Block Transformers]
    end

    %% API Layer
    subgraph "API Layer (tRPC)"
        TL[timeline.get]
        TS[transitionStep]
        SUB[Subscriptions]
        VAL[Validators]
        AGG[Aggregators]
    end

    %% Business Logic
    subgraph "Business Logic"
        SM[Step State Machine]
        EH[Event Handlers]
        SE[Side Effects]
        AUTH[Authorization]
    end

    %% Data Layer
    subgraph "Database Layer"
        MS[MatchStep Table]
        ACT[Action Table]
        MAT[Match Table]
        COMP[JobCompensation Table]
    end

    %% User Flow
    UIA -->|1. User Action| UI
    UI -->|2. Call Handler| CTX
    CTX -->|3. Mutation| TS

    %% Write Flow
    TS -->|4. Validate| VAL
    VAL -->|5. Authorize| AUTH
    AUTH -->|6. Process| SM
    SM -->|7. Update State| MS
    SM -->|8. Create History| ACT
    SM -->|9. Side Effects| SE
    SE -->|10. Update Related| COMP

    %% Read Flow
    UI -->|Request Timeline| HOOK
    HOOK -->|Query| TL
    TL -->|Aggregate| AGG
    AGG -->|Fetch Current| MS
    AGG -->|Fetch History| ACT
    AGG -->|Fetch Context| MAT
    TL -->|11. Transform| TRANS
    TRANS -->|12. Return| UIB

    %% Real-time
    SUB -->|Push Updates| UIC
    UIC -->|Refresh| UI

    %% Styling
    classDef userLayer fill:#e1f5e1,stroke:#4caf50,stroke-width:2px
    classDef frontendLayer fill:#e3f2fd,stroke:#2196f3,stroke-width:2px
    classDef apiLayer fill:#fff3e0,stroke:#ff9800,stroke-width:2px
    classDef bizLayer fill:#fce4ec,stroke:#e91e63,stroke-width:2px
    classDef dataLayer fill:#f3e5f5,stroke:#9c27b0,stroke-width:2px

    class UI,UIA,UIB,UIC userLayer
    class CTX,HOOK,REG,TRANS frontendLayer
    class TL,TS,SUB,VAL,AGG apiLayer
    class SM,EH,SE,AUTH bizLayer
    class MS,ACT,MAT,COMP dataLayer
```

### Data Flow Pipeline

#### 1. **User Journey Flow**

```
User Action → UI Component → Context Hook → API Call → State Update → UI Refresh
```

**Example: Provider Accepts Rate Offer**

1. User clicks "Accept $150/hr" button
2. `RateOfferBlock` calls `onAccept` handler
3. `MatchContext.acceptRate()` triggered
4. `transitionStep` mutation called with:
   ```typescript
   {
     action: "UPDATE",
     stepType: "RATE_NEGOTIATION",
     metadata: {
       offerType: "ACCEPT",
       acceptedRate: 150
     }
   }
   ```
5. API validates, processes, and returns updated state
6. Timeline refreshes with new "rate-negotiated" block

#### 2. **Backend Processing Pipeline**

```
API Endpoint → Authorization → Validation → State Machine → Database Updates → Side Effects
```

**Detailed Backend Flow:**

```typescript
// 1. API receives transition request
transitionStep.mutation(input) {
  // 2. Authorization check
  await authorize(user, match, stepType);

  // 3. Validate transition is allowed
  validateTransition(currentStep, input.action);

  // 4. Type-safe metadata validation
  validateMetadata(input.stepType, input.metadata);

  // 5. Execute state transition
  const result = await stepStateMachine.transition({
    current: currentStep,
    action: input.action,
    metadata: input.metadata
  });

  // 6. Database updates (transactional)
  await prisma.$transaction([
    // Update MatchStep with new state
    updateMatchStep(stepId, result.newState),

    // Create Action record for history
    createAction({
      type: mapToActionType(input),
      metadata: enrichMetadata(input)
    }),

    // Update related entities
    ...result.sideEffects
  ]);

  // 7. Trigger async side effects
  await triggerSideEffects(result);

  // 8. Return enriched response
  return buildResponse(result);
}
```

#### 3. **Frontend Data Transformation**

```
Raw API Data → Type Guards → Transformers → UI-Ready Components
```

**Transform Pipeline:**

```typescript
// 1. Receive timeline data from API
const rawTimeline = await api.jobs.matches.timeline.get({ matchId });

// 2. Transform to UI milestones
const milestones = rawTimeline.events.map(event => {
  // Type narrowing based on stepType
  const transformer = StepTransformers[event.type];

  // Convert to milestone structure
  return transformer.toMilestone(event);
});

// 3. Group into logical sections
const groupedMilestones = groupByPhase(milestones);

// 4. Apply UI-specific enhancements
const enhancedMilestones = addUIMetadata(groupedMilestones);

// 5. Pass to Timeline component
<NegotiationTimeline milestones={enhancedMilestones} />
```

### Database Schema Relationships

```mermaid
erDiagram
    Match ||--o{ MatchStep : has
    Match ||--o| JobCompensation : negotiates
    Match ||--o{ Action : tracks
    MatchStep ||--o{ Action : generates

    Match {
        string id PK
        string status
        string providerId FK
        string organizationId FK
        string jobId FK
    }

    MatchStep {
        string id PK
        string matchId FK
        enum type
        enum status
        json metadata
        datetime startedAt
        datetime completedAt
    }

    Action {
        string id PK
        string resourceId FK
        enum type
        json metadata
        string actorId FK
        datetime createdAt
    }

    JobCompensation {
        string id PK
        string matchId FK
        float currentOfferRate
        float finalAgreedRate
        json rateHistory
    }
```

### API Integration Points

#### Simplified API Surface

```typescript
// PRIMARY: Single timeline query - aggregates everything
api.jobs.matches.timeline.get({ matchId }) → {
  // Complete timeline ready for UI consumption
  milestones: Milestone[]        // UI-ready milestone blocks
  summary: TimelineSummary       // Current phase, progress, next actions
  context: MatchContext          // Match, provider, organization, job data
}

// SECONDARY: Single transition endpoint for all state changes
api.jobs.matches.timeline.transition({
  matchId: string
  stepType: StepType
  action: "START" | "UPDATE" | "COMPLETE" | "FAIL" | "SKIP"
  metadata: StepTypeMetadata     // Type-safe based on stepType
}) → {
  success: boolean
  updatedTimeline: Milestone[]   // Fresh timeline after transition
  nextActions: ActionItem[]      // What user can do next
}

// OPTIONAL: Real-time updates
api.jobs.matches.timeline.subscribe({ matchId }) → Stream<TimelineUpdate>
```

#### Backend Timeline Aggregation

**Single Query Does Everything:**

```typescript
// timeline.get() aggregates from multiple sources
async function getTimeline({ matchId }) {
  // 1. Get all data in one efficient query
  const data = await prisma.match.findUnique({
    where: { id: matchId },
    include: {
      // Get everything we need in one go
      steps: {
        include: {
          actions: true, // All step actions
        },
      },
      actions: true, // All match actions
      compensation: true, // Rate negotiation data
      provider: {
        include: { person: true },
      },
      organization: true,
      job: true,
    },
  });

  // 2. Build complete timeline server-side
  const timeline = await TimelineBuilder.build(data);

  // 3. Transform to UI-ready milestones
  const milestones = timeline.events.map((event) =>
    StepTransformers[event.stepType].toMilestone(event),
  );

  // 4. Add UI metadata and actions
  const enrichedMilestones = enrichWithUIActions(milestones, data);

  // 5. Return everything the UI needs
  return {
    milestones: enrichedMilestones,
    summary: buildSummary(timeline),
    context: buildContext(data),
  };
}
```

#### Frontend Simplification

**Ultra-Simple Frontend:**

```typescript
// Single hook for everything
function useMatchTimeline(matchId: string) {
  const { data, isLoading, mutate } = api.jobs.matches.timeline.get.useQuery({
    matchId
  });

  const transition = api.jobs.matches.timeline.transition.useMutation({
    onSuccess: (result) => {
      // Replace timeline with fresh data from server
      mutate(result.updatedTimeline);
    }
  });

  return {
    // UI-ready data (no transformation needed)
    milestones: data?.milestones ?? [],
    summary: data?.summary,
    context: data?.context,
    loading: isLoading,

    // Single action method for all transitions
    transitionStep: transition.mutate
  };
}

// Component becomes dead simple
function MatchTimeline({ matchId }: { matchId: string }) {
  const { milestones, loading, transitionStep } = useMatchTimeline(matchId);

  if (loading) return <TimelineSkeleton />;

  return (
    <NegotiationTimeline
      milestones={milestones}  // Already UI-ready
      onAction={transitionStep} // Single handler for all actions
    />
  );
}
```

#### Data Flow Simplified

```
User Action → transitionStep → Backend Aggregation → Fresh Timeline → UI Update
```

**Example: Rate Acceptance**

1. User clicks "Accept $150/hr"
2. `transitionStep({ stepType: "RATE_NEGOTIATION", action: "UPDATE", metadata: { offerType: "ACCEPT", rate: 150 } })`
3. Backend processes transition, updates database, rebuilds timeline
4. Returns fresh `milestones` array with new "rate-negotiated" block
5. UI replaces timeline with server response

### Simplified Architecture Diagram

```mermaid
graph TB
    subgraph "Frontend (Simplified)"
        UI[Timeline UI]
        HOOK[useMatchTimeline]
    end

    subgraph "API (Minimal Surface)"
        TLG[timeline.get]
        TLT[timeline.transition]
    end

    subgraph "Backend (Does Heavy Lifting)"
        AGG[Timeline Aggregator]
        TRANS[Step Transformer]
        SM[State Machine]
    end

    subgraph "Database"
        TABLES[(Match + Steps + Actions)]
    end

    %% Simple flow
    UI -->|1. Load Timeline| HOOK
    HOOK -->|2. Query| TLG
    TLG -->|3. Aggregate| AGG
    AGG -->|4. Fetch| TABLES
    AGG -->|5. Transform| TRANS
    TRANS -->|6. UI-Ready Data| UI

    %% Action flow
    UI -->|7. User Action| TLT
    TLT -->|8. Process| SM
    SM -->|9. Update| TABLES
    SM -->|10. Fresh Timeline| AGG
    AGG -->|11. Return Updated| UI

    classDef simple fill:#e8f5e8,stroke:#4caf50,stroke-width:3px
    class UI,HOOK,TLG,TLT simple
```

### Benefits of Simplified Architecture

1. **Single Source of Truth**: `timeline.get()` is the only way to get timeline data
2. **Server-Side Aggregation**: Complex joins and transformations happen once on the server
3. **Type Safety**: Server returns UI-ready, fully typed data
4. **Performance**: One query instead of multiple round trips
5. **Consistency**: Timeline is always fresh and complete
6. **Simplicity**: Frontend just renders what server provides

This approach follows the principle: **"Make the common case simple and the complex case possible."** The timeline is the common case, so we make it dead simple.

## Core Architecture: Unified Step Management

### Overview

A single, consistent method for handling all match steps that:

- Drives state transitions through the match workflow
- Stores immutable history via Actions
- Maintains typed metadata in MatchStep
- Provides rich timeline data for UI experiences

## Timeline Architecture & Contract

### 1. **Timeline Data Structure**

```typescript
interface TimelineResponse {
  milestones: Milestone[]; // Ordered by timestamp (newest first)
  summary: TimelineSummary; // Current state and progress
  context: MatchContext; // Background data for UI
}

interface Milestone {
  id: string; // MatchStep.id
  type: StepType; // RATE_NEGOTIATION, IDENTITY_VERIFICATION, etc.
  status: StepStatus;
  title: string; // Human-readable step name
  timestamp: Date; // Step creation/start time
  completedAt?: Date; // When step was completed
  order: number; // Step order in workflow

  // All timeline events for this step (actions + messages)
  blocks: TimelineBlock[]; // Ordered by timestamp

  // Step-specific metadata for UI context
  metadata: MatchStepMetadata;
}

interface TimelineBlock {
  id: string; // Action.id or Message.id
  type: BlockType; // "action" | "message" | "status-change"
  subType: string; // "rate-offer" | "background-check" | "text-message"
  timestamp: Date; // When this event occurred
  actor: {
    id: string;
    type: "PROVIDER" | "ORGANIZATION" | "SYSTEM";
    name: string;
    avatar?: string;
  };
  data: BlockData; // Type-safe data for UI block rendering
  source: "ACTION" | "MESSAGE" | "STATUS";
}
```

### 2. **Timeline Aggregation Engine**

```typescript
// Core aggregation process in timeline.get()
class TimelineAggregator {
  async build(matchId: string): Promise<TimelineResponse> {
    // 1. Fetch all raw data in one query
    const rawData = await this.fetchMatchData(matchId);

    // 2. Build milestones from MatchSteps
    const milestones = await this.buildMilestones(rawData.steps);

    // 3. Aggregate all timeline events
    const events = await this.aggregateEvents(rawData);

    // 4. Distribute events into appropriate milestones
    const populatedMilestones = this.distributeEvents(milestones, events);

    // 5. Transform to UI-ready format
    const transformedMilestones =
      await this.transformForUI(populatedMilestones);

    return {
      milestones: transformedMilestones,
      summary: this.buildSummary(rawData),
      context: this.buildContext(rawData),
    };
  }

  private async aggregateEvents(data: MatchData): Promise<TimelineEvent[]> {
    const events: TimelineEvent[] = [];

    // Add all Actions as events
    for (const action of data.actions) {
      events.push({
        id: action.id,
        timestamp: action.createdAt,
        source: "ACTION",
        stepType: action.metadata.stepType,
        data: action.metadata,
        actor: await this.resolveActor(action.actorId),
      });
    }

    // Add all Messages as events (from Thread/Message table)
    for (const message of data.messages) {
      events.push({
        id: message.id,
        timestamp: message.createdAt,
        source: "MESSAGE",
        stepType: this.inferStepFromMessage(message), // Context-based inference
        data: message,
        actor: await this.resolveActor(message.authorId),
      });
    }

    // Add status changes as events
    for (const statusChange of data.statusChanges) {
      events.push({
        id: `status-${statusChange.id}`,
        timestamp: statusChange.createdAt,
        source: "STATUS",
        stepType: statusChange.stepType,
        data: statusChange,
        actor: { type: "SYSTEM", name: "System" },
      });
    }

    return events.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
  }
}
```

### 3. **Extensible Block Registry**

```typescript
// Registry for transforming events to UI blocks
interface BlockTransformer {
  canHandle: (event: TimelineEvent) => boolean;
  transform: (event: TimelineEvent) => TimelineBlock;
  priority: number; // For handling conflicts
}

class BlockRegistry {
  private transformers: Map<string, BlockTransformer[]> = new Map();

  register(stepType: StepType, transformer: BlockTransformer) {
    if (!this.transformers.has(stepType)) {
      this.transformers.set(stepType, []);
    }
    this.transformers.get(stepType)!.push(transformer);
  }

  transform(event: TimelineEvent): TimelineBlock {
    const transformers = this.transformers.get(event.stepType) || [];

    // Find the right transformer
    const transformer = transformers
      .filter((t) => t.canHandle(event))
      .sort((a, b) => b.priority - a.priority)[0];

    if (!transformer) {
      return this.createFallbackBlock(event);
    }

    return transformer.transform(event);
  }
}

// Example transformers for Rate Negotiation
const rateNegotiationTransformers = [
  {
    canHandle: (event) =>
      event.stepType === "RATE_NEGOTIATION" &&
      event.source === "ACTION" &&
      event.data.offerType === "INITIAL",
    transform: (event) => ({
      id: event.id,
      type: "action",
      subType: "rate-offer",
      timestamp: event.timestamp,
      actor: event.actor,
      data: {
        rate: `$${event.data.proposedRate}/hr`,
        status: "pending",
        expectedParty:
          event.data.lastOfferBy === "PROVIDER" ? "organization" : "provider",
        onAccept: () =>
          transitionStep({
            action: "UPDATE",
            metadata: { offerType: "ACCEPT" },
          }),
        onCounter: () =>
          transitionStep({
            action: "UPDATE",
            metadata: { offerType: "COUNTER" },
          }),
      },
    }),
    priority: 10,
  },
  {
    canHandle: (event) =>
      event.stepType === "RATE_NEGOTIATION" && event.source === "MESSAGE",
    transform: (event) => ({
      id: event.id,
      type: "message",
      subType: "text-message",
      timestamp: event.timestamp,
      actor: event.actor,
      data: {
        author: event.actor.name,
        message: event.data.content,
        timestamp: event.timestamp,
      },
    }),
    priority: 5,
  },
];
```

### 4. **Step-to-Milestone Mapping**

```typescript
// Each StepType has a defined milestone structure
const StepMilestoneConfig = {
  RATE_NEGOTIATION: {
    title: "Rate Negotiation",
    description: "Negotiate compensation terms",
    phases: ["initiation", "negotiation", "agreement"],
    blockTypes: ["rate-offer", "rate-negotiated", "message"],
    defaultStatus: "active",
  },
  IDENTITY_VERIFICATION: {
    title: "Identity Verification",
    description: "Verify provider identity documents",
    phases: ["submission", "review", "approval"],
    blockTypes: ["identity-verification", "document-upload", "message"],
    defaultStatus: "pending",
  },
  BACKGROUND_CHECK: {
    title: "Background Check",
    description: "Complete background verification",
    phases: ["initiation", "processing", "results"],
    blockTypes: ["background-check", "background-results", "message"],
    defaultStatus: "pending",
  },
  INTERVIEW: {
    title: "Interview",
    description: "Conduct provider interview",
    phases: ["scheduling", "interview", "evaluation"],
    blockTypes: ["interview-scheduled", "interview-completed", "message"],
    defaultStatus: "pending",
  },
  // ... for each StepType
};

// Milestone builder uses this config
class MilestoneBuilder {
  build(step: MatchStep): Milestone {
    const config = StepMilestoneConfig[step.type];

    return {
      id: step.id,
      stepType: step.type,
      title: config.title,
      status: this.mapStatus(step.status),
      timestamp: step.createdAt,
      completedAt: step.completedAt,
      order: step.order,
      blocks: [], // Will be populated by event distribution
      metadata: step.metadata,
    };
  }
}
```

### 5. **Message Integration Strategy**

```typescript
// Messages are contextualized within steps
interface MessageContextualizer {
  // Infer which step a message belongs to
  inferStepContext(message: Message, match: Match): StepType {
    // 1. Check if message has explicit step reference
    if (message.stepId) {
      return this.getStepType(message.stepId);
    }

    // 2. Use timing and match state to infer context
    const activeSteps = match.steps.filter(s => s.status === "IN_PROGRESS");
    if (activeSteps.length === 1) {
      return activeSteps[0].type;
    }

    // 3. Use message content analysis (keywords, mentions)
    return this.analyzeMessageContent(message.content);
  }

  // Create message blocks within appropriate milestones
  createMessageBlock(message: Message, stepType: StepType): TimelineBlock {
    return {
      id: message.id,
      type: "message",
      subType: "text-message",
      timestamp: message.createdAt,
      actor: this.resolveActor(message.authorId),
      data: {
        author: message.author.name,
        message: message.content,
        timestamp: message.createdAt,
        avatar: message.author.avatar
      }
    };
  }
}
```

### 6. **API Contract Consistency**

```typescript
// Standardized response format for all timeline queries
interface StandardTimelineResponse<T extends MatchStepMetadata = any> {
  // Core timeline data
  milestones: Milestone<T>[];

  // Summary for UI chrome
  summary: {
    currentPhase: string; // "Verification" | "Negotiation" | "Finalization"
    totalSteps: number;
    completedSteps: number;
    activeStep?: StepType;
    progress: number; // 0-100 percentage
    nextActions: ActionItem[]; // What user can do next
  };

  // Context data for UI
  context: {
    match: {
      id: string;
      status: MatchStatus;
      initiator: MatchInitiator;
      createdAt: Date;
    };
    provider: {
      id: string;
      name: string;
      avatar?: string;
      specialty: string;
    };
    organization: {
      id: string;
      name: string;
      avatar?: string;
    };
    job: {
      id: string;
      title: string;
      role: string;
      location?: string;
    };
  };

  // Metadata for UI behavior
  ui: {
    allowedTransitions: Record<StepType, string[]>; // What actions are available
    expandedByDefault: StepType[]; // Which milestones start expanded
    showMessageInput: boolean; // Whether to show message input
    realTimeUpdates: boolean; // Whether this timeline has live updates
  };
}
```

### 7. **Extensibility Pattern**

```typescript
// Adding new step types is straightforward
class TimelineExtensions {
  static addStepType(stepType: StepType, config: StepConfig) {
    // 1. Register milestone config
    StepMilestoneConfig[stepType] = config;

    // 2. Register block transformers
    config.transformers.forEach((transformer) => {
      BlockRegistry.register(stepType, transformer);
    });

    // 3. Register UI components (if needed)
    UIBlockRegistry.register(stepType, config.uiComponents);
  }
}

// Example: Adding a new "Drug Screening" step
TimelineExtensions.addStepType(StepType.DRUG_SCREENING, {
  title: "Drug Screening",
  description: "Complete drug screening requirements",
  phases: ["scheduling", "testing", "results"],
  blockTypes: ["drug-screening-scheduled", "drug-screening-completed"],
  transformers: [
    {
      canHandle: (event) =>
        event.stepType === "DRUG_SCREENING" &&
        event.data.status === "scheduled",
      transform: (event) => ({
        type: "action",
        subType: "drug-screening-scheduled",
        data: {
          appointmentDate: event.data.appointmentDate,
          location: event.data.location,
          instructions: event.data.instructions,
        },
      }),
    },
  ],
});
```

## Benefits of This Architecture

1. **Consistent API Contract**: Every timeline response follows the same structure
2. **Extensible**: Easy to add new step types without changing core logic
3. **Message Integration**: Messages are naturally woven into the appropriate timeline context
4. **Type Safety**: Full TypeScript support from database to UI
5. **Performance**: Single query aggregates everything efficiently
6. **Maintainable**: Clear separation between data aggregation and UI transformation

This architecture ensures that as we add new step types (drug screening, contract signing, orientation, etc.), we follow the same patterns and the frontend automatically gets the right UI blocks without additional code changes.

import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import type { MatchContextValue } from "@/components/widgets/match/context/MatchContext";

import { MatchNegotiationPanel } from "@/components/widgets/match";
import { MatchContext } from "@/components/widgets/match/context/MatchContext";

// Create a story wrapper that provides mock data to the real MatchProvider
function StoryMatchProvider({
  children,
  mockData,
  loading = false,
  error = null,
}: {
  children: React.ReactNode;
  mockData?: MatchContextValue;
  loading?: boolean;
  error?: Error | null;
}) {
  const contextValue: MatchContextValue = mockData || {
    match: null,
    loading: false,
    error: null,
    acceptRate: async () => {},
    counterRate: async () => {},
    submitOffer: async () => {},
    sendMessage: async () => {},
    currentRate: null,
    isNegotiating: false,
    canAcceptRate: false,
    canCounterRate: false,
    providerName: "",
    providerAvatar: null,
    providerSpecialty: "",
    jobTitle: "",
    jobRole: "",
    jobPaymentType: "",
    jobSalary: null,
    organizationName: "",
    organizationAvatar: null,
  };

  return (
    <MatchContext.Provider
      value={{
        ...contextValue,
        loading,
        error,
      }}
    >
      {children}
    </MatchContext.Provider>
  );
}

const meta: Meta<typeof MatchNegotiationPanel> = {
  title: "Widgets/Match/MatchNegotiationPanel",
  component: MatchNegotiationPanel,
  parameters: {
    layout: "centered",
  },
  decorators: [
    (Story, { args }) => (
      <div className="h-96 w-96">
        <Story {...args} />
      </div>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof MatchNegotiationPanel>;

// Helper function to create base mock match data
function createBaseMockMatch(overrides: any = {}): MatchContextValue {
  return {
    // Data
    match: {
      id: "match-123",
      status: "NEGOTIATING",
      initiator: "PROVIDER",
      initiationNote:
        "I'm interested in this position and would like to discuss the rate.",
      createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
      updatedAt: new Date(Date.now() - 6 * 60 * 60 * 1000), // 6 hours ago
      jobId: "job-456",
      providerId: "provider-789",
      organizationId: "org-101",
      applicationId: null,
      offerId: null,
      positionId: null,
      contractId: null,
      threadId: null,
      initiatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
      initiatedBy: "provider-789",
      job: {
        id: "job-456",
        status: "PUBLISHED",
        summary: "Emergency Department Physician",
        role: "Emergency Medicine",
        paymentType: "HOURLY",
        paymentAmount: 150,
        allowRateNegotiation: true,
        minNegotiableRate: 120,
        maxNegotiableRate: 200,
      },
      provider: {
        id: "provider-789",
        title: "Emergency Medicine Physician",
        person: {
          id: "person-111",
          firstName: "Dr. Sarah",
          lastName: "Johnson",
          avatar: null,
        },
      },
      organization: {
        id: "org-101",
        name: "City General Hospital",
        avatar: null,
      },
      compensation: {
        id: "comp-222",
        minRate: 120,
        maxRate: 200,
        currentOfferRate: 165,
        finalAgreedRate: null,
        rateStrategy: "balanced",
        negotiationCount: 2,
        negotiationStatus: "ACTIVE",
        lastOfferBy: "org-101",
        offerExpiresAt: null,
        paymentType: "HOURLY",
        createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
        updatedAt: new Date(Date.now() - 6 * 60 * 60 * 1000), // 6 hours ago
      },
      application: undefined,
      offer: undefined,
      position: undefined,
      contract: undefined,
      thread: undefined,
      steps: undefined,
      ...overrides.match,
    },
    loading: false,
    error: null,

    // Actions
    acceptRate: async () => console.log("Accept rate"),
    counterRate: async () => console.log("Counter rate"),
    submitOffer: async () => console.log("Submit offer"),
    sendMessage: async () => console.log("Send message"),

    // Computed values
    currentRate: 165,
    isNegotiating: true,
    canAcceptRate: true,
    canCounterRate: true,

    // Provider data
    providerName: "Dr. Sarah Johnson",
    providerAvatar: null,
    providerSpecialty: "Emergency Medicine Physician",

    // Job data
    jobTitle: "Emergency Department Physician",
    jobRole: "Emergency Medicine",
    jobPaymentType: "HOURLY",
    jobSalary: "$150",

    // Organization data
    organizationName: "City General Hospital",
    organizationAvatar: null,
    ...overrides,
  };
}

// Base mock context data - simple active negotiation
const mockMatchData: MatchContextValue = createBaseMockMatch();

export const Default: Story = {
  args: {
    defaultExpanded: true,
    showMessageInput: true,
    showHeader: true,
  },
  decorators: [
    (Story) => (
      <StoryMatchProvider loading={false} error={null} mockData={mockMatchData}>
        <Story />
      </StoryMatchProvider>
    ),
  ],
};

export const Loading: Story = {
  args: {
    defaultExpanded: true,
    showMessageInput: true,
    showHeader: true,
  },
  decorators: [
    (Story) => (
      <StoryMatchProvider loading={true}>
        <Story />
      </StoryMatchProvider>
    ),
  ],
};

export const Errored: Story = {
  args: {
    defaultExpanded: true,
    showMessageInput: true,
    showHeader: true,
  },
  decorators: [
    (Story) => (
      <StoryMatchProvider error={new Error("Failed to load negotiation data")}>
        <Story />
      </StoryMatchProvider>
    ),
  ],
};

export const Collapsed: Story = {
  args: {
    defaultExpanded: false,
    showMessageInput: true,
    showHeader: true,
  },
  decorators: [
    (Story) => (
      <StoryMatchProvider loading={false} error={null} mockData={mockMatchData}>
        <Story />
      </StoryMatchProvider>
    ),
  ],
};

export const NoMessageInput: Story = {
  args: {
    defaultExpanded: true,
    showMessageInput: false,
    showHeader: true,
  },
  decorators: [
    (Story) => (
      <StoryMatchProvider loading={false} error={null} mockData={mockMatchData}>
        <Story />
      </StoryMatchProvider>
    ),
  ],
};

// Multiple Rounds of Negotiation - Back and forth offers
export const MultipleRounds: Story = {
  args: {
    defaultExpanded: true,
    showMessageInput: true,
    showHeader: true,
  },
  decorators: [
    (Story) => (
      <StoryMatchProvider
        loading={false}
        error={null}
        mockData={createBaseMockMatch({
          match: {
            compensation: {
              id: "comp-333",
              minRate: 120,
              maxRate: 200,
              currentOfferRate: 175,
              finalAgreedRate: null,
              rateStrategy: "aggressive",
              negotiationCount: 5,
              negotiationStatus: "ACTIVE",
              lastOfferBy: "provider-789",
              offerExpiresAt: new Date("2024-01-20T17:00:00Z"),
              paymentType: "HOURLY",
              createdAt: new Date("2024-01-15T10:00:00Z"),
              updatedAt: new Date("2024-01-16T16:00:00Z"),
              history: [
                {
                  id: "offer-1",
                  rate: 180,
                  offeredBy: "provider-789",
                  offeredAt: new Date("2024-01-15T10:30:00Z"),
                  status: "DECLINED",
                  message:
                    "Looking for competitive rate for emergency medicine expertise",
                  respondedAt: new Date("2024-01-15T11:45:00Z"),
                },
                {
                  id: "offer-2",
                  rate: 155,
                  offeredBy: "org-101",
                  offeredAt: new Date("2024-01-15T12:00:00Z"),
                  status: "DECLINED",
                  message:
                    "Our budget allows for $155/hr given the current market conditions",
                  respondedAt: new Date("2024-01-15T13:30:00Z"),
                },
                {
                  id: "offer-3",
                  rate: 170,
                  offeredBy: "provider-789",
                  offeredAt: new Date("2024-01-15T14:00:00Z"),
                  status: "DECLINED",
                  message:
                    "I can come down to $170/hr considering the regular schedule",
                  respondedAt: new Date("2024-01-16T08:30:00Z"),
                },
                {
                  id: "offer-4",
                  rate: 165,
                  offeredBy: "org-101",
                  offeredAt: new Date("2024-01-16T09:00:00Z"),
                  status: "DECLINED",
                  message:
                    "We can meet at $165/hr, which is competitive for our area",
                  respondedAt: new Date("2024-01-16T15:30:00Z"),
                },
                {
                  id: "offer-5",
                  rate: 175,
                  offeredBy: "provider-789",
                  offeredAt: new Date("2024-01-16T16:00:00Z"),
                  status: "PENDING",
                  message:
                    "Final offer at $175/hr - reflects my board certification and trauma experience",
                  respondedAt: null,
                },
              ],
            },
          },
          currentRate: 175,
          canAcceptRate: false, // Organization needs to respond
          canCounterRate: false,
        })}
      >
        <Story />
      </StoryMatchProvider>
    ),
  ],
  parameters: {
    docs: {
      description: {
        story:
          "Shows a complex negotiation with 5 rounds of back-and-forth offers between provider and organization.",
      },
    },
  },
};

// Successfully Completed Negotiation
export const SuccessfulNegotiation: Story = {
  args: {
    defaultExpanded: true,
    showMessageInput: false,
    showHeader: true,
  },
  decorators: [
    (Story) => (
      <StoryMatchProvider
        loading={false}
        error={null}
        mockData={createBaseMockMatch({
          match: {
            status: "FINALIZING",
            compensation: {
              id: "comp-444",
              minRate: 140,
              maxRate: 180,
              currentOfferRate: 160,
              finalAgreedRate: 160,
              rateStrategy: "balanced",
              negotiationCount: 3,
              negotiationStatus: "ACCEPTED",
              lastOfferBy: "org-101",
              offerExpiresAt: null,
              paymentType: "HOURLY",
              agreedAt: new Date("2024-01-16T11:30:00Z"),
              createdAt: new Date("2024-01-15T09:00:00Z"),
              updatedAt: new Date("2024-01-16T11:30:00Z"),
              history: [
                {
                  id: "offer-1",
                  rate: 170,
                  offeredBy: "provider-789",
                  offeredAt: new Date("2024-01-15T09:00:00Z"),
                  status: "DECLINED",
                  message: "Requesting $170/hr based on my ICU experience",
                  respondedAt: new Date("2024-01-15T14:30:00Z"),
                },
                {
                  id: "offer-2",
                  rate: 155,
                  offeredBy: "org-101",
                  offeredAt: new Date("2024-01-15T15:00:00Z"),
                  status: "DECLINED",
                  message: "Our range tops out at $155/hr for this position",
                  respondedAt: new Date("2024-01-16T09:30:00Z"),
                },
                {
                  id: "offer-3",
                  rate: 160,
                  offeredBy: "org-101",
                  offeredAt: new Date("2024-01-16T10:00:00Z"),
                  status: "ACCEPTED",
                  message:
                    "Final offer at $160/hr, includes benefits package and PTO",
                  respondedAt: new Date("2024-01-16T11:30:00Z"),
                },
              ],
            },
          },
          currentRate: 160,
          isNegotiating: false,
          canAcceptRate: false,
          canCounterRate: false,
        })}
      >
        <Story />
      </StoryMatchProvider>
    ),
  ],
  parameters: {
    docs: {
      description: {
        story:
          "Shows a completed negotiation where both parties reached an agreement.",
      },
    },
  },
};

// Failed/Expired Negotiation
export const ExpiredNegotiation: Story = {
  args: {
    defaultExpanded: true,
    showMessageInput: false,
    showHeader: true,
  },
  decorators: [
    (Story) => (
      <StoryMatchProvider
        loading={false}
        error={null}
        mockData={createBaseMockMatch({
          match: {
            status: "EXPIRED",
            compensation: {
              id: "comp-555",
              minRate: 100,
              maxRate: 150,
              currentOfferRate: 140,
              finalAgreedRate: null,
              rateStrategy: "budget_conscious",
              negotiationCount: 4,
              negotiationStatus: "EXPIRED",
              lastOfferBy: "provider-789",
              offerExpiresAt: new Date(Date.now() - 24 * 60 * 60 * 1000), // Expired 24 hours ago
              paymentType: "HOURLY",
              createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7 days ago
              updatedAt: new Date(Date.now() - 25 * 60 * 60 * 1000), // 25 hours ago
              history: [
                {
                  id: "offer-1",
                  rate: 130,
                  offeredBy: "org-101",
                  offeredAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7 days ago
                  status: "DECLINED",
                  message:
                    "Opening offer at $130/hr for general medicine coverage",
                  respondedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), // 5 days ago
                },
                {
                  id: "offer-2",
                  rate: 165,
                  offeredBy: "provider-789",
                  offeredAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), // 5 days ago
                  status: "DECLINED",
                  message:
                    "Market rate for internal medicine is $165/hr minimum",
                  respondedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
                },
                {
                  id: "offer-3",
                  rate: 135,
                  offeredBy: "org-101",
                  offeredAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
                  status: "DECLINED",
                  message: "Best we can do is $135/hr - budget constraints",
                  respondedAt: new Date(Date.now() - 25 * 60 * 60 * 1000), // 25 hours ago
                },
                {
                  id: "offer-4",
                  rate: 140,
                  offeredBy: "provider-789",
                  offeredAt: new Date(Date.now() - 25 * 60 * 60 * 1000), // 25 hours ago
                  status: "EXPIRED",
                  message: "Final counter at $140/hr - expires tonight",
                  respondedAt: null,
                },
              ],
            },
          },
          currentRate: 140,
          isNegotiating: false,
          canAcceptRate: false,
          canCounterRate: false,
        })}
      >
        <Story />
      </StoryMatchProvider>
    ),
  ],
  parameters: {
    docs: {
      description: {
        story:
          "Shows a negotiation that expired without reaching agreement - parties were too far apart on rate expectations.",
      },
    },
  },
};

// High-Stakes Surgical Position Negotiation
export const HighStakesNegotiation: Story = {
  args: {
    defaultExpanded: true,
    showMessageInput: true,
    showHeader: true,
  },
  decorators: [
    (Story) => (
      <StoryMatchProvider
        loading={false}
        error={null}
        mockData={createBaseMockMatch({
          match: {
            job: {
              id: "job-surgical",
              status: "PUBLISHED",
              summary: "Cardiac Surgeon - Heart Transplant Program",
              role: "Cardiac Surgery",
              paymentType: "HOURLY",
              paymentAmount: 400,
              allowRateNegotiation: true,
              minNegotiableRate: 350,
              maxNegotiableRate: 500,
            },
            provider: {
              id: "provider-789",
              title: "Cardiac Surgeon",
              person: {
                id: "person-111",
                firstName: "Dr. Michael",
                lastName: "Chen",
                avatar: null,
              },
            },
            compensation: {
              id: "comp-666",
              minRate: 350,
              maxRate: 500,
              currentOfferRate: 450,
              finalAgreedRate: null,
              rateStrategy: "premium",
              negotiationCount: 3,
              negotiationStatus: "ACTIVE",
              lastOfferBy: "provider-789",
              offerExpiresAt: new Date("2024-01-18T17:00:00Z"),
              paymentType: "HOURLY",
              createdAt: new Date("2024-01-15T08:00:00Z"),
              updatedAt: new Date("2024-01-16T12:00:00Z"),
              history: [
                {
                  id: "offer-1",
                  rate: 400,
                  offeredBy: "org-101",
                  offeredAt: new Date("2024-01-15T08:00:00Z"),
                  status: "DECLINED",
                  message:
                    "Opening offer for cardiac surgery position with transplant privileges",
                  respondedAt: new Date("2024-01-15T13:30:00Z"),
                },
                {
                  id: "offer-2",
                  rate: 475,
                  offeredBy: "provider-789",
                  offeredAt: new Date("2024-01-15T14:00:00Z"),
                  status: "DECLINED",
                  message:
                    "Requesting $475/hr - reflects 15+ years transplant experience and fellowship training",
                  respondedAt: new Date("2024-01-16T11:30:00Z"),
                },
                {
                  id: "offer-3",
                  rate: 450,
                  offeredBy: "provider-789",
                  offeredAt: new Date("2024-01-16T12:00:00Z"),
                  status: "PENDING",
                  message:
                    "Counter at $450/hr considering the prestige of your program",
                  respondedAt: null,
                },
              ],
            },
          },
          currentRate: 450,
          providerName: "Dr. Michael Chen",
          providerSpecialty: "Cardiac Surgeon",
          jobTitle: "Cardiac Surgeon - Heart Transplant Program",
          jobRole: "Cardiac Surgery",
          jobSalary: "$400",
          canAcceptRate: false, // Organization's turn
          canCounterRate: false,
        })}
      >
        <Story />
      </StoryMatchProvider>
    ),
  ],
  parameters: {
    docs: {
      description: {
        story:
          "High-stakes negotiation for a specialized cardiac surgery position with premium rates.",
      },
    },
  },
};

// Quick Acceptance - Provider accepted first offer
export const QuickAcceptance: Story = {
  args: {
    defaultExpanded: true,
    showMessageInput: false,
    showHeader: true,
  },
  decorators: [
    (Story) => (
      <StoryMatchProvider
        loading={false}
        error={null}
        mockData={createBaseMockMatch({
          match: {
            status: "MATCHED",
            compensation: {
              id: "comp-777",
              minRate: 120,
              maxRate: 160,
              currentOfferRate: 145,
              finalAgreedRate: 145,
              rateStrategy: "competitive",
              negotiationCount: 1,
              negotiationStatus: "ACCEPTED",
              lastOfferBy: "org-101",
              offerExpiresAt: null,
              paymentType: "HOURLY",
              agreedAt: new Date("2024-01-15T11:15:00Z"),
              createdAt: new Date("2024-01-15T10:30:00Z"),
              updatedAt: new Date("2024-01-15T11:15:00Z"),
              history: [
                {
                  id: "offer-1",
                  rate: 145,
                  offeredBy: "org-101",
                  offeredAt: new Date("2024-01-15T10:30:00Z"),
                  status: "ACCEPTED",
                  message:
                    "Competitive offer at $145/hr with excellent benefits package and flexible scheduling",
                  respondedAt: new Date("2024-01-15T11:15:00Z"),
                },
              ],
            },
          },
          currentRate: 145,
          isNegotiating: false,
          canAcceptRate: false,
          canCounterRate: false,
        })}
      >
        <Story />
      </StoryMatchProvider>
    ),
  ],
  parameters: {
    docs: {
      description: {
        story:
          "Simple scenario where provider immediately accepted the organization's first competitive offer.",
      },
    },
  },
};

// Provider High Ask - Testing the waters
export const ProviderHighAsk: Story = {
  args: {
    defaultExpanded: true,
    showMessageInput: true,
    showHeader: true,
  },
  decorators: [
    (Story) => (
      <StoryMatchProvider
        loading={false}
        error={null}
        mockData={createBaseMockMatch({
          match: {
            job: {
              id: "job-456",
              status: "PUBLISHED",
              summary: "Night Shift Emergency Physician",
              role: "Emergency Medicine",
              paymentType: "HOURLY",
              paymentAmount: 160,
              allowRateNegotiation: true,
              minNegotiableRate: 140,
              maxNegotiableRate: 190,
            },
            compensation: {
              id: "comp-888",
              minRate: 140,
              maxRate: 190,
              currentOfferRate: 185,
              finalAgreedRate: null,
              rateStrategy: "aggressive",
              negotiationCount: 2,
              negotiationStatus: "ACTIVE",
              lastOfferBy: "provider-789",
              offerExpiresAt: new Date("2024-01-17T23:59:59Z"),
              paymentType: "HOURLY",
              createdAt: new Date("2024-01-15T13:00:00Z"),
              updatedAt: new Date("2024-01-16T10:00:00Z"),
              history: [
                {
                  id: "offer-1",
                  rate: 195,
                  offeredBy: "provider-789",
                  offeredAt: new Date("2024-01-15T13:00:00Z"),
                  status: "DECLINED",
                  message:
                    "Night shift premium request - $195/hr for overnight emergency coverage",
                  respondedAt: new Date("2024-01-16T09:30:00Z"),
                },
                {
                  id: "offer-2",
                  rate: 185,
                  offeredBy: "provider-789",
                  offeredAt: new Date("2024-01-16T10:00:00Z"),
                  status: "PENDING",
                  message:
                    "Revised to $185/hr - still reflects night differential and emergency expertise",
                  respondedAt: null,
                },
              ],
            },
          },
          currentRate: 185,
          jobTitle: "Night Shift Emergency Physician",
          canAcceptRate: false, // Organization needs to respond
          canCounterRate: false,
        })}
      >
        <Story />
      </StoryMatchProvider>
    ),
  ],
  parameters: {
    docs: {
      description: {
        story:
          "Provider started with a high ask above the posted range, testing the organization's flexibility.",
      },
    },
  },
};

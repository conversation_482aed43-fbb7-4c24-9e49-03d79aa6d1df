import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import type { MatchContextValue } from "@/components/widgets/match/context/MatchContext";

import { MatchCockpit } from "@/components/widgets/match";
import { MatchContext } from "@/components/widgets/match/context/MatchContext";

// Create a story wrapper that provides mock data to the real MatchProvider
function StoryMatchProvider({
  children,
  mockData,
  loading = false,
  error = null,
}: {
  children: React.ReactNode;
  mockData?: MatchContextValue;
  loading?: boolean;
  error?: Error | null;
}) {
  const contextValue: MatchContextValue = mockData || {
    match: null,
    loading: false,
    error: null,
    acceptRate: async () => {},
    counterRate: async () => {},
    submitOffer: async () => {},
    sendMessage: async () => {},
    currentRate: null,
    isNegotiating: false,
    canAcceptRate: false,
    canCounterRate: false,
    providerName: "",
    providerAvatar: null,
    providerSpecialty: "",
    jobTitle: "",
    jobRole: "",
    jobPaymentType: "",
    jobSalary: null,
    organizationName: "",
    organizationAvatar: null,
  };

  return (
    <MatchContext.Provider
      value={{
        ...contextValue,
        loading,
        error,
      }}
    >
      {children}
    </MatchContext.Provider>
  );
}

const meta: Meta<typeof MatchCockpit> = {
  title: "Widgets/Match/MatchCockpit",
  component: MatchCockpit,
  parameters: {
    layout: "fullscreen",
  },
};

export default meta;
type Story = StoryObj<typeof MatchCockpit>;

// Base mock context data
const mockMatchData: MatchContextValue = {
  // Data
  match: {
    id: "match-123",
    status: "NEGOTIATING",
    initiator: "PROVIDER",
    initiationNote:
      "I'm interested in this position and would like to discuss the rate.",
    createdAt: new Date("2024-01-15T10:00:00Z"),
    updatedAt: new Date("2024-01-15T14:30:00Z"),
    jobId: "job-456",
    providerId: "provider-789",
    organizationId: "org-101",
    applicationId: null,
    offerId: null,
    positionId: null,
    contractId: null,
    threadId: null,
    initiatedAt: new Date("2024-01-15T10:00:00Z"),
    initiatedBy: "provider-789",
    job: {
      id: "job-456",
      status: "PUBLISHED",
      summary: "Emergency Department Physician",
      role: "Emergency Medicine",
      paymentType: "HOURLY",
      paymentAmount: 150,
      allowRateNegotiation: true,
      minNegotiableRate: 120,
      maxNegotiableRate: 200,
    },
    provider: {
      id: "provider-789",
      title: "Emergency Medicine Physician",
      person: {
        id: "person-111",
        firstName: "Dr. Sarah",
        lastName: "Johnson",
        avatar: null,
      },
    },
    organization: {
      id: "org-101",
      name: "City General Hospital",
      avatar: null,
    },
    compensation: {
      id: "comp-222",
      minRate: 120,
      maxRate: 200,
      currentOfferRate: 165,
      finalAgreedRate: null,
      rateStrategy: "balanced",
      negotiationCount: 2,
      negotiationStatus: "ACTIVE",
      lastOfferBy: "org-101",
      offerExpiresAt: null,
      paymentType: "HOURLY",
    },
    application: undefined,
    offer: undefined,
    position: undefined,
    contract: undefined,
    thread: undefined,
    steps: undefined,
  },
  loading: false,
  error: null,

  // Actions
  acceptRate: async () => {
    console.log("Accept rate");
  },
  counterRate: async () => {
    console.log("Counter rate");
  },
  submitOffer: async () => {
    console.log("Submit offer");
  },
  sendMessage: async () => {
    console.log("Send message");
  },

  // Computed values
  currentRate: 165,
  isNegotiating: true,
  canAcceptRate: true,
  canCounterRate: true,

  // Provider data
  providerName: "Dr. Sarah Johnson",
  providerAvatar: null,
  providerSpecialty: "Emergency Medicine Physician",

  // Job data
  jobTitle: "Emergency Department Physician",
  jobRole: "Emergency Medicine",
  jobPaymentType: "HOURLY",
  jobSalary: "$150",

  // Organization data
  organizationName: "City General Hospital",
  organizationAvatar: null,
};

export const Default: Story = {
  args: {
    onViewJobDetails: () => alert("View job details clicked"),
    showNegotiationHeader: true,
    defaultNegotiationExpanded: true,
    showMessageInput: true,
  },
  decorators: [
    (Story) => (
      <StoryMatchProvider loading={false} error={null} mockData={mockMatchData}>
        <div className="h-screen">
          <Story />
        </div>
      </StoryMatchProvider>
    ),
  ],
};

export const Loading: Story = {
  args: {
    onViewJobDetails: () => alert("View job details clicked"),
  },
  decorators: [
    (Story) => (
      <StoryMatchProvider loading={true}>
        <div className="h-screen">
          <Story />
        </div>
      </StoryMatchProvider>
    ),
  ],
};

export const Errored: Story = {
  args: {
    onViewJobDetails: () => alert("View job details clicked"),
  },
  decorators: [
    (Story) => (
      <StoryMatchProvider error={new Error("Failed to load match data")}>
        <div className="h-screen">
          <Story />
        </div>
      </StoryMatchProvider>
    ),
  ],
};

export const MinimalLayout: Story = {
  args: {
    onViewJobDetails: () => alert("View job details clicked"),
    showNegotiationHeader: false,
    defaultNegotiationExpanded: false,
    showMessageInput: false,
  },
  decorators: [
    (Story) => (
      <StoryMatchProvider loading={false} error={null} mockData={mockMatchData}>
        <div className="h-screen">
          <Story />
        </div>
      </StoryMatchProvider>
    ),
  ],
};

export const HighRateNegotiation: Story = {
  args: {
    onViewJobDetails: () => alert("View job details clicked"),
    showNegotiationHeader: true,
    defaultNegotiationExpanded: true,
    showMessageInput: true,
  },
  decorators: [
    (Story) => (
      <StoryMatchProvider
        mockData={{
          ...mockMatchData,
          currentRate: 225,
          match: {
            ...mockMatchData.match!,
            compensation: {
              ...mockMatchData.match!.compensation!,
              currentOfferRate: 225,
              maxRate: 250,
              lastOfferBy: "provider-789",
              negotiationCount: 5,
            },
          },
        }}
      >
        <div className="h-screen">
          <Story />
        </div>
      </StoryMatchProvider>
    ),
  ],
};

"use client";

import { use<PERSON><PERSON>back, useMemo } from "react";
import { PlusCircleIcon } from "lucide-react";
import { useFormContext } from "react-hook-form";

import type { SelectorProps } from "@axa/ui/selectors/Selector";
import { cn } from "@axa/ui/lib";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@axa/ui/primitives/form";
import { Skeleton } from "@axa/ui/primitives/skeleton";
import { useSearchValue } from "@axa/ui/search/value";
import { Selector } from "@axa/ui/selectors/Selector";

import type { JobPost } from "@/hooks/selectors/use-select-job-post";

import { useSelectJobPost } from "@/hooks/selectors/use-select-job-post";

const i18n = {
  en: {
    label: "Job",
    description: "Select a job",
    placeholder: "Select a job",
    actions: {
      selectJob: "Select Job",
    },
  },
};

export type JobStructure = JobPost;
type PartialJobPostSelector = Omit<SelectorProps<JobPost>, "data">;

export interface SelectJobPostProps
  extends Omit<PartialJobPostSelector, "data"> {
  loading?: boolean;
  enabled?: boolean;
  organizationId?: string;
  defaultQuery?: string;
  defaultSelection?: JobPost;
  defaultDebounce?: number;
  pageSize?: number;
  pageNumber?: number;
  pending?: boolean;
  size?: PartialJobPostSelector["size"];
}

interface SelectJobPostCoreProps extends PartialJobPostSelector {
  data?: JobPost[];
  loading?: boolean;
  selection?: JobPost;
  open?: boolean;
  query?: string;
  onOpenChange?: (open: boolean) => void;
  onValueChange?: (value: string) => void;
  onSelect?: (jobPost: JobPost) => void | Promise<void>;
  onClear?: () => void;
  pending?: boolean;
}

function SelectJobPostCore({
  data = [],
  loading = false,
  selection,
  open = false,
  query = "",
  onOpenChange,
  onValueChange,
  onSelect,
  onClear,
  useDialog = false,
  defaultValue,
  children,
  size = "lg",
  className,
  placeholder = i18n.en.placeholder,
  pending = false,
  ...props
}: SelectJobPostCoreProps) {
  const enhancedData = useMemo(() => {
    const jobPosts = [...data];
    if (selection && !jobPosts.find((jobPost) => jobPost.id === selection.id)) {
      jobPosts.unshift(selection);
    }
    return jobPosts;
  }, [data, selection]);

  const renderItem = useCallback(
    (item: JobPost) => (
      <div className="flex flex-col gap-1 truncate text-start">
        <h3
          className={cn("truncate text-base font-semibold", {
            "text-sm": size === "sm",
            "text-base": size === "md",
            "text-lg": size === "lg",
          })}
        >
          {item.summary}
        </h3>
        <div
          className={cn("flex gap-2 truncate text-xs text-muted-foreground", {
            "text-xs": size === "sm",
            "text-sm": size === "md",
            "text-base": size === "lg",
          })}
        >
          <span>Role: {item.role}</span>
          <span>Status: {item.status}</span>
        </div>
        {item.organization && (
          <p
            className={cn("truncate text-sm text-muted-foreground", {
              "text-xs": size === "sm",
              "text-sm": size === "md",
              "text-base": size === "lg",
            })}
          >
            {item.organization.name}
          </p>
        )}
      </div>
    ),
    [size],
  );

  const renderLoading = useCallback(
    () => (
      <div className="flex flex-col gap-1 text-start">
        <Skeleton className="h-5 w-32" />
        <Skeleton className="h-4 w-20" />
      </div>
    ),
    [],
  );

  return (
    <Selector<JobPost>
      useDialog={useDialog}
      defaultValue={defaultValue}
      size={size}
      className={cn(
        {
          "h-12 p-2": size === "md",
          "h-9 p-2": size === "sm",
          "min-h-8 py-2": size === "lg",
        },
        className,
      )}
      loading={loading}
      pending={pending}
      label={placeholder}
      placeholder={placeholder}
      {...props}
      data={enhancedData}
      open={open}
      onOpenChange={onOpenChange}
      value={query}
      selection={selection}
      onValueChange={onValueChange}
      onSelect={onSelect}
      onClear={onClear}
      renderItem={renderItem}
      renderLoading={renderLoading}
    >
      {children ?? (
        <>
          <PlusCircleIcon size="20" color="currentColor" />
          <span>{i18n.en.actions.selectJob}</span>
        </>
      )}
    </Selector>
  );
}

export function SelectJobPost({
  enabled = true,
  loading = false,
  defaultValue,
  onSelect,
  organizationId,
  defaultQuery,
  defaultSelection,
  defaultDebounce = 500,
  pageSize = 5,
  pageNumber = 0,
  ...props
}: SelectJobPostProps) {
  const {
    data,
    loading: hookLoading,
    selection,
    open,
    query,
    setSelection,
    setQuery,
    setOpen,
  } = useSelectJobPost({
    enabled,
    organizationId,
    pageSize,
    pageNumber,
    defaultQuery: defaultValue ?? defaultQuery,
    defaultSelection,
    defaultDebounce,
    onSelect,
  });

  const isLoading = loading || hookLoading;

  return (
    <SelectJobPostCore
      {...props}
      data={data}
      loading={isLoading}
      selection={selection ?? undefined}
      open={open}
      query={query}
      onOpenChange={setOpen}
      onValueChange={setQuery}
      onSelect={setSelection}
    />
  );
}

export interface SelectJobPostFieldProps extends SelectJobPostProps {
  name?: string;
  label?: string;
  description?: string;
  placeholder?: string;
  showLabel?: boolean;
  showDescription?: boolean;
}

export function SelectJobPostField({
  name = "job",
  label = i18n.en.label,
  description = i18n.en.description,
  placeholder = i18n.en.placeholder,
  showLabel = true,
  showDescription = true,
  ...props
}: SelectJobPostFieldProps) {
  const { control } = useFormContext();

  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel
            className={cn({
              "sr-only": !showLabel,
            })}
          >
            {label}
          </FormLabel>
          <FormDescription
            className={cn({
              "sr-only": !showDescription,
            })}
          >
            {description}
          </FormDescription>
          <FormControl>
            <SelectJobPost
              {...props}
              placeholder={placeholder}
              value={field.value as string}
              onSelect={(value) => {
                field.onChange(value.id);
              }}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}

export interface SearchJobPostProps extends SelectJobPostProps {
  group: string;
  name?: string;
  defaultValue?: string;
}

export function SearchJobPost({
  group,
  name = "jobPost",
  defaultValue,
  enabled = true,
  loading = false,
  organizationId,
  defaultQuery,
  defaultSelection,
  defaultDebounce = 500,
  pageSize = 5,
  pageNumber = 0,
  onSelect,
  useDialog = false,
  ...props
}: SearchJobPostProps) {
  const {
    data,
    loading: hookLoading,
    open,
    query,
    setSelection,
    setQuery,
    setOpen,
  } = useSelectJobPost({
    enabled,
    organizationId,
    pageSize,
    pageNumber,
    defaultQuery: defaultValue ?? defaultQuery,
    defaultSelection,
    defaultDebounce,
    onSelect,
  });

  const { selection, onClear, onSelectionChange } = useSearchValue<JobPost>({
    name,
    group,
    defaultValue,
    data,
  });

  const isLoading = loading || hookLoading;

  const handleSelect = useCallback(
    async (jobPost: JobPost) => {
      await setSelection(jobPost);
      await onSelect?.(jobPost);
      onSelectionChange(jobPost);
    },
    [setSelection, onSelectionChange, onSelect],
  );

  return (
    <SelectJobPostCore
      {...props}
      data={data}
      loading={isLoading}
      selection={selection ?? undefined}
      open={open}
      query={query}
      onOpenChange={setOpen}
      onValueChange={setQuery}
      onSelect={handleSelect}
      onClear={onClear}
      useDialog={useDialog}
      size="sm"
    />
  );
}

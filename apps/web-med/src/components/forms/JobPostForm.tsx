"use client";

import type { PropsWithChildren } from "react";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useFormContext } from "react-hook-form";
import { z } from "zod";

import type { ButtonProps } from "@axa/ui/primitives/button";
import { DescriptionField } from "@axa/ui/fields/text/Description";
import { TextField } from "@axa/ui/fields/text/Text";
// import { TextEditor } from "@axa/ui/editors/RichTextEditor";
import { cn } from "@axa/ui/lib";
import { Button } from "@axa/ui/primitives/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@axa/ui/primitives/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@axa/ui/primitives/select";

import { JobPostMode, JobPostType } from "@/api";

const i18n = {
  en: {
    summary: {
      label: "Summary",
      description: "A brief summary of the job post",
      placeholder: "Enter a brief summary of the job post",
    },
    description: {
      label: "Description",
      description: "Detailed description of the job post",
      placeholder: "Enter the job description",
    },
    type: {
      label: "Job Type",
      description: "The type of job",
      placeholder: "Select the job type",
      options: {
        [JobPostType.PERMANENT]: "Permanent",
        [JobPostType.TEMPORARY]: "Temporary",
        [JobPostType.PER_DIEM]: "Per Diem",
      },
    },
    paymentType: {
      label: "Payment Type",
      description: "The type of payment",
      placeholder: "Select the payment type",
      options: {
        HOURLY: "Hourly",
        FIXED: "Fixed",
      },
    },
    paymentAmount: {
      label: "Pay Amount",
      description: "The pay amount for the job",
      placeholder: "Enter the pay amount",
    },
    mode: {
      label: "Mode",
      description: "The mode of the job post",
      placeholder: "Select the mode",
      options: {
        [JobPostMode.ASSISTED]: "Assisted",
        [JobPostMode.INDEPENDENT]: "Independent",
      },
    },
    actions: {
      submit: "Submit",
    },
    messages: {
      summary: {
        min: "Summary must be at least 5 characters long",
      },
      description: {
        min: "Description must be at least 10 characters long",
      },
    },
  },
};

const jobPostFormSchema = z.object({
  organizationId: z.string(),
  role: z.string(),
  summary: z
    .string()
    .min(5, { message: "Summary must be at least 5 characters long" }),
  description: z
    .string()
    .min(10, { message: "Description must be at least 10 characters long" }),
  type: z.nativeEnum(JobPostType).default(JobPostType.PER_DIEM),
  mode: z.nativeEnum(JobPostMode).default(JobPostMode.ASSISTED),
});

export type JobPostFormValues = z.infer<typeof jobPostFormSchema>;
export type JobPostFormProps = PropsWithChildren<
  Parameters<typeof useForm<JobPostFormValues>>[0] & {
    onSubmit?: (values: JobPostFormValues) => void | Promise<void>;
  }
>;

export default function JobPostForm({
  children,
  onSubmit = () => void 0,
  ...props
}: JobPostFormProps) {
  const form = useForm<JobPostFormValues>({
    ...props,
    resolver: zodResolver(jobPostFormSchema),
  });

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <TextField
          name="summary"
          label={i18n.en.summary.label}
          description={i18n.en.summary.description}
          placeholder={i18n.en.summary.placeholder}
        />

        <DescriptionField
          name="description"
          label={i18n.en.description.label}
          description={i18n.en.description.description}
          placeholder={i18n.en.description.placeholder}
        />

        <FormField
          control={form.control}
          name="type"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{i18n.en.type.label}</FormLabel>
              <FormDescription>{i18n.en.type.description}</FormDescription>
              <FormControl>
                <Select value={field.value} onValueChange={field.onChange}>
                  <SelectTrigger>
                    <SelectValue placeholder={i18n.en.type.placeholder} />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.entries(i18n.en.type.options).map(
                      ([value, label]) => (
                        <SelectItem key={value} value={value}>
                          {label}
                        </SelectItem>
                      ),
                    )}
                  </SelectContent>
                </Select>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* <FormField
          control={form.control}
          name="mode"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{i18n.en.mode.label}</FormLabel>
              <FormDescription>{i18n.en.mode.description}</FormDescription>
              <FormControl>
                <div className="mx-auto flex w-fit items-center gap-2">
                  <span>{i18n.en.mode.options[JobPostMode.INDEPENDENT]}</span>
                  <Switch
                    checked={field.value === JobPostMode.ASSISTED}
                    onCheckedChange={(value) =>
                      field.onChange(
                        value ? JobPostMode.ASSISTED : JobPostMode.INDEPENDENT,
                      )
                    }
                  />
                  <span>{i18n.en.mode.options[JobPostMode.ASSISTED]}</span>
                </div>
              </FormControl>
            </FormItem>
          )}
        /> */}

        {children ?? (
          <div className="flex w-full justify-center">
            <JobPostFormSubmitButton />
          </div>
        )}
      </form>
    </Form>
  );
}

export function JobPostFormSubmitButton({
  children = i18n.en.actions.submit,
  ...props
}: ButtonProps) {
  const form = useFormContext<JobPostFormValues>();

  return (
    <Button
      {...props}
      className={cn("min-w-40", props.className)}
      disabled={
        // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
        props.disabled || form.formState.isSubmitting || !form.formState.isDirty
      }
      type="submit"
    >
      {children}
    </Button>
  );
}

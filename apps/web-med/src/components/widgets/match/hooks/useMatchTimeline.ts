"use client";

import { useMemo } from "react";

import type { Milestone } from "@axa/ui/blocks/negotiation-timeline";

import type { RouterOutputs } from "@/api";

interface UseMatchTimelineProps {
  match: RouterOutputs["jobs"]["matches"]["get"];
  onAcceptRate?: (rate: number) => void;
  onCounterRate?: (rate: number, message?: string) => void;
}

export function useMatchTimeline({
  match,
  onAcceptRate,
  onCounterRate,
}: UseMatchTimelineProps): Milestone[] {
  return useMemo(() => {
    if (!match) return [];

    const milestones: Milestone[] = [];

    // Rate Negotiation Milestone
    if (match.compensation || match.status === "NEGOTIATING") {
      const rateBlocks = [];

      // Current rate offer block
      if (match.compensation?.currentOfferRate) {
        const rate = match.compensation.currentOfferRate;
        const isProvider = match.compensation.lastOfferBy === match.providerId;
        const isPending = match.compensation.negotiationStatus === "ACTIVE";

        rateBlocks.push({
          id: `rate-offer-${match.compensation.id}`,
          type: "rate-offer" as const,
          timestamp: new Date(match.compensation.updatedAt || match.createdAt),
          data: {
            rate: `$${rate}/hr`,
            status: isPending ? ("pending" as const) : ("accepted" as const),
            expectedParty: isPending
              ? isProvider
                ? "organization"
                : "provider"
              : undefined,
            onAccept:
              isPending && onAcceptRate ? () => onAcceptRate(rate) : undefined,
            onCounter:
              isPending && onCounterRate
                ? () => {
                    // Simple counter - in real app, this would open a dialog
                    const counterRate = prompt(
                      `Counter offer (current: $${rate}/hr):`,
                    );
                    if (counterRate) {
                      onCounterRate(parseFloat(counterRate));
                    }
                  }
                : undefined,
          },
        });
      }

      // Final negotiated rate (if completed)
      if (match.compensation?.finalAgreedRate) {
        const providerName = `${match.provider?.person.firstName} ${match.provider?.person.lastName}`;

        rateBlocks.push({
          id: `rate-final-${match.compensation.id}`,
          type: "rate-negotiated" as const,
          timestamp: new Date(match.compensation.updatedAt || match.createdAt),
          data: {
            doctor: providerName,
            rate: `$${match.compensation.finalAgreedRate}/hr`,
            message: "Rate negotiation completed successfully.",
            onViewAgreement: () => {
              // TODO: Implement view agreement
              alert("View agreement functionality coming soon");
            },
            onViewNegotiationHistory: () => {
              // TODO: Implement view history
              alert("View negotiation history coming soon");
            },
          },
        });
      }

      if (rateBlocks.length > 0) {
        milestones.push({
          id: "rate-negotiation",
          title: "Rate Negotiation",
          status: match.status === "MATCHED" ? "completed" : "active",
          timestamp: new Date(match.createdAt),
          blocks: rateBlocks,
        });
      }
    }

    // Initial match creation milestone
    milestones.push({
      id: "match-created",
      title: "Match Created",
      status: "completed",
      timestamp: new Date(match.createdAt),
      blocks: [
        {
          id: `match-init-${match.id}`,
          type: "message" as const,
          timestamp: new Date(match.createdAt),
          data: {
            author:
              match.initiator === "PROVIDER" ? "Provider" : "Organization",
            message:
              match.initiationNote || "Match initiated for this position.",
            timestamp: new Date(match.createdAt),
          },
        },
      ],
    });

    // Sort milestones by timestamp (newest first)
    return milestones.sort(
      (a, b) => b.timestamp.getTime() - a.timestamp.getTime(),
    );
  }, [match, onAcceptRate, onCounterRate]);
}

"use client";

import { createContext, useCallback, useContext, useMemo } from "react";

import type { RouterOutputs } from "@/api";

import { api } from "@/api/client";

// Types
export interface MatchContextValue {
  // Data
  match: RouterOutputs["jobs"]["matches"]["get"] | null;
  loading: boolean;
  error: Error | null;

  // Actions
  acceptRate: (rate: number) => Promise<void>;
  counterRate: (rate: number, message?: string) => Promise<void>;
  submitOffer: (rate: number, message?: string) => Promise<void>;
  sendMessage: (message: string) => Promise<void>;

  // Computed values
  currentRate: number | null;
  isNegotiating: boolean;
  canAcceptRate: boolean;
  canCounterRate: boolean;

  // Provider data
  providerName: string;
  providerAvatar: string | null;
  providerSpecialty: string;

  // Job data
  jobTitle: string;
  jobRole: string;
  jobPaymentType: string;
  jobSalary: string | null;

  // Organization data
  organizationName: string;
  organizationAvatar: string | null;
}

export const MatchContext = createContext<MatchContextValue | null>(null);

export interface MatchProviderProps {
  matchId: string;
  children: React.ReactNode;
}

export function MatchProvider({ matchId, children }: MatchProviderProps) {
  // Fetch match data
  const {
    data: match,
    isLoading: loading,
    error,
  } = api.jobs.matches.get.useQuery(
    {
      id: matchId,
      include: {
        job: true,
        provider: true,
        organization: true,
        compensation: true,
        steps: true,
      },
    },
    { enabled: !!matchId },
  );

  // Mutations
  const submitOfferMutation = api.jobs.matches.rates.submitOffer.useMutation();
  const respondToOfferMutation =
    api.jobs.matches.rates.respondToOffer.useMutation();

  // Actions
  const acceptRate = useCallback(async (rate: number) => {
    if (!match?.compensation) return;

    await respondToOfferMutation.mutateAsync({
      matchId: match.id,
      offerId: "current", // TODO: Get actual offer ID
      response: "ACCEPT",
    });
  }, []);

  const counterRate = useCallback(async (rate: number, message?: string) => {
    if (!match) return;

    await submitOfferMutation.mutateAsync({
      matchId: match.id,
      proposedRate: rate,
      message,
    });
  }, []);

  const submitOffer = useCallback(async (rate: number, message?: string) => {
    if (!match) return;

    await submitOfferMutation.mutateAsync({
      matchId: match.id,
      proposedRate: rate,
      message,
    });
  }, []);

  const sendMessage = useCallback(async (message: string) => {
    // TODO: Implement message API
    console.log("Send message:", message);
  }, []);

  // Computed values
  const contextValue = useMemo((): MatchContextValue => {
    const currentRate = match?.compensation?.currentOfferRate || null;
    const isNegotiating = match?.status === "NEGOTIATING";
    const isPending = match?.compensation?.negotiationStatus === "ACTIVE";

    return {
      // Data
      match: match || null,
      loading,
      error: error as Error | null,

      // Actions
      acceptRate,
      counterRate,
      submitOffer,
      sendMessage,

      // Computed values
      currentRate,
      isNegotiating,
      canAcceptRate: isPending && !!currentRate,
      canCounterRate: isPending && !!currentRate,

      // Provider data
      providerName: match?.provider
        ? `${match.provider.person.firstName} ${match.provider.person.lastName}`
        : "",
      providerAvatar: match?.provider?.person.avatar || null,
      providerSpecialty: match?.provider?.title || "Healthcare Provider",

      // Job data
      jobTitle: match?.job?.summary || "",
      jobRole: match?.job?.role || "",
      jobPaymentType: match?.job?.paymentType || "",
      jobSalary: match?.job?.paymentAmount
        ? `$${match.job.paymentAmount}`
        : null,

      // Organization data
      organizationName: match?.organization?.name || "",
      organizationAvatar: match?.organization?.avatar || null,
    };
  }, [
    match,
    loading,
    error,
    acceptRate,
    counterRate,
    submitOffer,
    sendMessage,
  ]);

  return (
    <MatchContext.Provider value={contextValue}>
      {children}
    </MatchContext.Provider>
  );
}

export function useMatch() {
  const context = useContext(MatchContext);
  if (!context) {
    throw new Error("useMatch must be used within a MatchProvider");
  }
  return context;
}

// MatchContextValue is already exported above

"use client";

import { RateInsightsPanel } from "@axa/ui/blocks/negotiation-center";
import { Skeleton } from "@axa/ui/primitives/skeleton";

import { useMatch } from "../context/MatchContext";

export interface MatchRateInsightsProps {
  className?: string;
}

export function MatchRateInsights({ className }: MatchRateInsightsProps) {
  const { 
    loading,
    currentRate,
    match,
  } = useMatch();

  if (loading) {
    return (
      <div className={className}>
        <div className="space-y-4 p-4">
          <div className="text-center">
            <Skeleton className="mx-auto h-8 w-24" />
            <Skeleton className="mx-auto mt-2 h-4 w-16" />
          </div>
          <div className="space-y-3">
            <div className="flex justify-between">
              <Skeleton className="h-4 w-20" />
              <Skeleton className="h-4 w-12" />
            </div>
            <div className="flex justify-between">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-4 w-16" />
            </div>
            <div className="flex justify-between">
              <Skeleton className="h-4 w-18" />
              <Skeleton className="h-4 w-14" />
            </div>
          </div>
          <div className="space-y-2">
            <Skeleton className="h-3 w-full" />
            <Skeleton className="h-3 w-3/4" />
          </div>
        </div>
      </div>
    );
  }

  const displayRate = currentRate || match?.job?.paymentAmount || 0;

  // TODO: Replace with real market data
  const rateHistory = Array.from({ length: 6 }, (_, i) => ({
    month: new Date(Date.now() - (5 - i) * 30 * 24 * 60 * 60 * 1000).toLocaleDateString('en-US', { month: 'short' }),
    rate: displayRate + (Math.random() - 0.5) * 20,
  }));

  return (
    <div className={className}>
      <RateInsightsPanel
        currentRate={displayRate}
        currency="$"
        unit="per hour"
        rateHistory={rateHistory}
        marketComparison={{
          percentile: 65,
          difference: 10,
          trend: "above",
        }}
        demandMetrics={{
          level: "medium",
          score: 65,
          trend: "stable",
        }}
        actionItems={[
          {
            id: "1",
            type: "opportunity",
            title: "Market Rate Above Average",
            description: "Current offer is 15% above market average",
            priority: "medium",
          },
          {
            id: "2", 
            type: "insight",
            title: "High Demand Specialty",
            description: "Emergency medicine has 85% demand score",
            priority: "high",
          },
        ]}
      />
    </div>
  );
}

"use client";

import { JobCard } from "@axa/ui/blocks/negotiation-center";
import { Skeleton } from "@axa/ui/primitives/skeleton";

import { useMatch } from "../context/MatchContext";

export interface MatchJobCardProps {
  className?: string;
  onViewDetails?: () => void;
}

export function MatchJobCard({ className, onViewDetails }: MatchJobCardProps) {
  const { 
    loading,
    match,
    jobTitle,
    jobRole,
    jobPaymentType,
    jobSalary,
    organizationName,
    organizationAvatar,
  } = useMatch();

  if (loading) {
    return (
      <div className={className}>
        <div className="space-y-4 p-4">
          <div className="space-y-2">
            <Skeleton className="h-5 w-48" />
            <Skeleton className="h-4 w-32" />
          </div>
          <div className="space-y-2">
            <Skeleton className="h-3 w-full" />
            <Skeleton className="h-3 w-2/3" />
            <Skeleton className="h-3 w-3/4" />
          </div>
          <div className="flex space-x-2">
            <Skeleton className="h-6 w-16 rounded-full" />
            <Skeleton className="h-6 w-20 rounded-full" />
          </div>
        </div>
      </div>
    );
  }

  // TODO: Get location from job data
  const location = match?.job ? {
    id: "location-1",
    name: "Main Campus",
    address: {
      formatted: "123 Hospital St, City, State 12345"
    }
  } : undefined;

  const organization = organizationName ? {
    id: match?.organizationId || "",
    name: organizationName,
    avatar: organizationAvatar,
    description: null,
  } : undefined;

  return (
    <div className={className}>
      <JobCard
        specialty={jobRole}
        location={location}
        schedule="Full-time" // TODO: Get from job schedule
        jobType={jobPaymentType}
        employmentType="Per Diem" // TODO: Get from job data
        salary={jobSalary || undefined}
        highlights={[
          "Emergency Department",
          "24/7 Coverage",
          "Level 1 Trauma Center"
        ]} // TODO: Get from job data
        organization={organization}
        onViewDetails={onViewDetails}
      />
    </div>
  );
}

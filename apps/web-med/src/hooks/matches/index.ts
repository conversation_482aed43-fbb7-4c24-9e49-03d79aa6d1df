import { useCallback, useMemo } from "react";

import { toast } from "@axa/ui/primitives/toast";

import type { RouterOutputs } from "@/api";

import { api } from "@/api/client";
import { useUser } from "@/components/contexts/User";

/**
 * Types for match operations
 */

// Match types
export type Match = NonNullable<RouterOutputs["jobs"]["matches"]["get"]>;
export type MatchStep = NonNullable<
  RouterOutputs["jobs"]["matches"]["steps"]["get"]
>;
export type MatchTimeline = NonNullable<
  RouterOutputs["jobs"]["matches"]["timeline"]["get"]
>;
export type MatchHistory = NonNullable<
  RouterOutputs["jobs"]["matches"]["timeline"]["get"]
>;
export type OfferHistory = NonNullable<
  RouterOutputs["jobs"]["matches"]["timeline"]["get"]
>;

// Rate negotiation types
export interface RateNegotiationMetadata {
  currentOfferRate: number;
  lastOfferBy: "PROVIDER" | "ORGANIZATION";
  lastOfferAt: string;
  offerExpiresAt?: string;
  offerHistory: RateOffer[];
  strategy: "conservative" | "competitive" | "balanced" | "premium";
  allowCounterOffers: boolean;
  maxNegotiationRounds: number;
  currentRound: number;
  threadId?: string;
  compensationId: string;
}

export interface RateOffer {
  id: string;
  proposedRate: number;
  madeBy: "PROVIDER" | "ORGANIZATION";
  madeAt: string;
  message?: string;
  expiresAt?: string;
  status: "PENDING" | "ACCEPTED" | "DECLINED" | "EXPIRED" | "WITHDRAWN";
  declineReason?: string;
  respondedAt?: string;
}

export type OfferResponse = "ACCEPT" | "DECLINE" | "COUNTER";

// Step types and statuses
export enum StepType {
  IDENTITY_VERIFICATION = "IDENTITY_VERIFICATION",
  BACKGROUND_CHECK = "BACKGROUND_CHECK",
  RATE_NEGOTIATION = "RATE_NEGOTIATION",
  INTERVIEW = "INTERVIEW",
  SCHEDULE_NEGOTIATION = "SCHEDULE_NEGOTIATION",
  BENEFITS_NEGOTIATION = "BENEFITS_NEGOTIATION",
  CONTRACT_TERMS = "CONTRACT_TERMS",
  REFERENCE_CHECK = "REFERENCE_CHECK",
  SKILLS_ASSESSMENT = "SKILLS_ASSESSMENT",
  DRUG_SCREENING = "DRUG_SCREENING",
  CREDENTIAL_VERIFICATION = "CREDENTIAL_VERIFICATION",
  FINAL_APPROVAL = "FINAL_APPROVAL",
  ONBOARDING_PREP = "ONBOARDING_PREP",
  EQUIPMENT_ASSIGNMENT = "EQUIPMENT_ASSIGNMENT",
  ORIENTATION_SCHEDULING = "ORIENTATION_SCHEDULING",
}

export enum StepStatus {
  PENDING = "PENDING",
  VALIDATING = "VALIDATING",
  IN_PROGRESS = "IN_PROGRESS",
  COMPLETED = "COMPLETED",
  SKIPPED = "SKIPPED",
  FAILED = "FAILED",
  CANCELLED = "CANCELLED",
}

// Timeline event types
export enum TimelineEventType {
  ACTION = "ACTION",
  MESSAGE = "MESSAGE",
  STEP = "STEP",
  OFFER = "OFFER",
  STATUS_CHANGE = "STATUS_CHANGE",
}

export enum TimelineActorType {
  PROVIDER = "PROVIDER",
  ORGANIZATION = "ORGANIZATION",
  SYSTEM = "SYSTEM",
  ADMIN = "ADMIN",
}

// Error messages
const i18n = {
  en: {
    error: {
      match: {
        get: "Failed to get match details",
        create: "Failed to create match",
        update: "Failed to update match",
        delete: "Failed to delete match",
      },
      step: {
        get: "Failed to get step details",
        create: "Failed to create step",
        start: "Failed to start step",
        complete: "Failed to complete step",
        fail: "Failed to mark step as failed",
      },
      rate: {
        start: "Failed to start rate negotiation",
        submit: "Failed to submit rate offer",
        respond: "Failed to respond to rate offer",
        finalize: "Failed to finalize rate negotiation",
      },
      timeline: {
        get: "Failed to get timeline",
      },
    },
  },
};

/**
 * Core match hooks (CRUD operations)
 */

/**
 * Hook for getting match details
 * @param matchId - The ID of the match to get
 * @returns Match details and loading state
 */
export function useMatch(matchId?: string) {
  const {
    data: match,
    isLoading,
    error,
  } = api.jobs.matches.get.useQuery(
    { id: matchId ?? "" },
    { enabled: !!matchId },
  );

  return useMemo(
    () => ({
      match,
      isLoading,
      error,
    }),
    [match, isLoading, error],
  );
}

/**
 * Hook for creating a match
 * @returns Functions for creating a match and loading state
 */
export function useCreateMatch() {
  const utils = api.useUtils();
  const createMatchMutation = api.jobs.matches.create.useMutation({
    onSuccess: async (newMatch) => {
      await utils.jobs.matches.get.invalidate({ id: newMatch.id });
    },
    onError: () => {
      toast.error(i18n.en.error.match.create);
    },
  });

  const createMatch = useCallback(
    async (data: {
      jobId: string;
      providerId: string;
      organizationId: string;
      initiator?: string;
      initiationNote?: string;
    }) => {
      return await createMatchMutation.mutateAsync(data);
    },
    [createMatchMutation],
  );

  return useMemo(
    () => ({
      createMatch,
      isLoading: createMatchMutation.isPending,
      error: createMatchMutation.error,
    }),
    [createMatch, createMatchMutation.isPending, createMatchMutation.error],
  );
}

/**
 * Hook for updating a match
 * @returns Functions for updating a match and loading state
 */
export function useUpdateMatch() {
  const utils = api.useUtils();
  const updateMatchMutation = api.jobs.matches.update.useMutation({
    onSuccess: async (updatedMatch) => {
      await utils.jobs.matches.get.invalidate({ id: updatedMatch.id });
    },
    onError: () => {
      toast.error(i18n.en.error.match.update);
    },
  });

  const updateMatch = useCallback(
    async (id: string, data: { status?: string; [key: string]: any }) => {
      return await updateMatchMutation.mutateAsync({ id, data });
    },
    [updateMatchMutation],
  );

  return useMemo(
    () => ({
      updateMatch,
      isLoading: updateMatchMutation.isPending,
      error: updateMatchMutation.error,
    }),
    [updateMatch, updateMatchMutation.isPending, updateMatchMutation.error],
  );
}

/**
 * Match step lifecycle management hooks
 */

/**
 * Hook for getting match step details
 * @param stepId - The ID of the step to get
 * @returns Step details and loading state
 */
export function useMatchStep(stepId?: string) {
  const {
    data: step,
    isLoading,
    error,
  } = api.jobs.matches.steps.get.useQuery(
    { id: stepId ?? "" },
    { enabled: !!stepId },
  );

  return useMemo(
    () => ({
      step,
      isLoading,
      error,
    }),
    [step, isLoading, error],
  );
}

/**
 * Hook for getting match step by match ID and type
 * @param matchId - The ID of the match
 * @param type - The type of step to get
 * @returns Step details and loading state
 */
export function useMatchStepByType(matchId?: string, type?: StepType) {
  const {
    data: step,
    isLoading,
    error,
  } = api.jobs.matches.steps.getByMatchAndType.useQuery(
    { matchId: matchId ?? "", type: type! },
    { enabled: !!matchId && !!type },
  );

  return useMemo(
    () => ({
      step,
      isLoading,
      error,
    }),
    [step, isLoading, error],
  );
}

/**
 * Hook for starting a match step
 * @returns Functions for starting a step and loading state
 */
export function useStartMatchStep() {
  const utils = api.useUtils();
  const startStepMutation = api.jobs.matches.steps.start.useMutation({
    onSuccess: async (step) => {
      await utils.jobs.matches.steps.get.invalidate({ id: step.id });
      await utils.jobs.matches.get.invalidate({ id: step.matchId });
    },
    onError: () => {
      toast.error(i18n.en.error.step.start);
    },
  });

  const startStep = useCallback(
    async (data: {
      matchId: string;
      type: StepType;
      metadata?: Record<string, any>;
      notes?: string;
    }) => {
      return await startStepMutation.mutateAsync(data);
    },
    [startStepMutation],
  );

  return useMemo(
    () => ({
      startStep,
      isLoading: startStepMutation.isPending,
      error: startStepMutation.error,
    }),
    [startStep, startStepMutation.isPending, startStepMutation.error],
  );
}

/**
 * Hook for completing a match step
 * @returns Functions for completing a step and loading state
 */
export function useCompleteMatchStep() {
  const utils = api.useUtils();
  const completeStepMutation = api.jobs.matches.steps.complete.useMutation({
    onSuccess: async (step) => {
      await utils.jobs.matches.steps.get.invalidate({ id: step.id });
      // Also invalidate the match to update its status
      const matchResult =
        await utils.jobs.matches.steps.getByMatchAndType.fetch({
          matchId: step.matchId,
          type: step.type as StepType,
        });
      if (matchResult) {
        await utils.jobs.matches.get.invalidate({ id: matchResult.matchId });
      }
    },
    onError: () => {
      toast.error(i18n.en.error.step.complete);
    },
  });

  const completeStep = useCallback(
    async (data: {
      id: string;
      isSuccessful?: boolean;
      metadata?: Record<string, any>;
      notes?: string;
    }) => {
      return await completeStepMutation.mutateAsync(data);
    },
    [completeStepMutation],
  );

  return useMemo(
    () => ({
      completeStep,
      isLoading: completeStepMutation.isPending,
      error: completeStepMutation.error,
    }),
    [completeStep, completeStepMutation.isPending, completeStepMutation.error],
  );
}

/**
 * Hook for failing a match step
 * @returns Functions for failing a step and loading state
 */
export function useFailMatchStep() {
  const utils = api.useUtils();
  const failStepMutation = api.jobs.matches.steps.fail.useMutation({
    onSuccess: async (step) => {
      await utils.jobs.matches.steps.get.invalidate({ id: step.id });
      // Also invalidate the match to update its status
      const matchResult =
        await utils.jobs.matches.steps.getByMatchAndType.fetch({
          matchId: step.matchId,
          type: step.type as StepType,
        });
      if (matchResult) {
        await utils.jobs.matches.get.invalidate({ id: matchResult.matchId });
      }
    },
    onError: () => {
      toast.error(i18n.en.error.step.fail);
    },
  });

  const failStep = useCallback(
    async (data: {
      id: string;
      reason: string;
      metadata?: Record<string, any>;
    }) => {
      return await failStepMutation.mutateAsync(data);
    },
    [failStepMutation],
  );

  return useMemo(
    () => ({
      failStep,
      isLoading: failStepMutation.isPending,
      error: failStepMutation.error,
    }),
    [failStep, failStepMutation.isPending, failStepMutation.error],
  );
}

/**
 * Rate negotiation hooks
 */

/**
 * Hook for starting rate negotiation
 * @returns Functions for starting rate negotiation and loading state
 */
export function useStartRateNegotiation() {
  const utils = api.useUtils();
  const startNegotiationMutation =
    api.jobs.matches.rates.startNegotiation.useMutation({
      onSuccess: async (result) => {
        await utils.jobs.matches.steps.get.invalidate({ id: result.step.id });
        await utils.jobs.matches.get.invalidate({ id: result.step.matchId });
      },
      onError: () => {
        toast.error(i18n.en.error.rate.start);
      },
    });

  const startNegotiation = useCallback(
    async (data: { matchId: string; initialRate?: number }) => {
      return await startNegotiationMutation.mutateAsync(data);
    },
    [startNegotiationMutation],
  );

  return useMemo(
    () => ({
      startNegotiation,
      isLoading: startNegotiationMutation.isPending,
      error: startNegotiationMutation.error,
    }),
    [
      startNegotiation,
      startNegotiationMutation.isPending,
      startNegotiationMutation.error,
    ],
  );
}

/**
 * Hook for submitting a rate offer
 * @returns Functions for submitting a rate offer and loading state
 */
export function useSubmitRateOffer() {
  const utils = api.useUtils();
  const submitOfferMutation = api.jobs.matches.rates.submitOffer.useMutation({
    onSuccess: async (result) => {
      // Invalidate the step to update its metadata
      await utils.jobs.matches.steps.getByMatchAndType.invalidate();
      // Also invalidate the match to update its status
      await utils.jobs.matches.get.invalidate();
      // Invalidate offer history
      await utils.jobs.matches.timeline.getOfferHistory.invalidate();
    },
    onError: () => {
      toast.error(i18n.en.error.rate.submit);
    },
  });

  const submitOffer = useCallback(
    async (data: {
      matchId: string;
      proposedRate: number;
      message?: string;
      expiresAt?: Date;
    }) => {
      return await submitOfferMutation.mutateAsync(data);
    },
    [submitOfferMutation],
  );

  return useMemo(
    () => ({
      submitOffer,
      isLoading: submitOfferMutation.isPending,
      error: submitOfferMutation.error,
    }),
    [submitOffer, submitOfferMutation.isPending, submitOfferMutation.error],
  );
}

/**
 * Hook for responding to a rate offer
 * @returns Functions for responding to a rate offer and loading state
 */
export function useRespondToRateOffer() {
  const utils = api.useUtils();
  const respondToOfferMutation =
    api.jobs.matches.rates.respondToOffer.useMutation({
      onSuccess: async (result) => {
        // Invalidate the step to update its metadata
        await utils.jobs.matches.steps.getByMatchAndType.invalidate();
        // Also invalidate the match to update its status
        await utils.jobs.matches.get.invalidate();
        // Invalidate offer history
        await utils.jobs.matches.timeline.getOfferHistory.invalidate();
      },
      onError: () => {
        toast.error(i18n.en.error.rate.respond);
      },
    });

  const respondToOffer = useCallback(
    async (data: {
      matchId: string;
      offerId: string;
      response: OfferResponse;
      counterOfferRate?: number;
      declineReason?: string;
      message?: string;
    }) => {
      return await respondToOfferMutation.mutateAsync(data);
    },
    [respondToOfferMutation],
  );

  return useMemo(
    () => ({
      respondToOffer,
      isLoading: respondToOfferMutation.isPending,
      error: respondToOfferMutation.error,
    }),
    [
      respondToOffer,
      respondToOfferMutation.isPending,
      respondToOfferMutation.error,
    ],
  );
}

/**
 * Hook for finalizing rate negotiation
 * @returns Functions for finalizing rate negotiation and loading state
 */
export function useFinalizeRateNegotiation() {
  const utils = api.useUtils();
  const finalizeNegotiationMutation =
    api.jobs.matches.rates.finalizeNegotiation.useMutation({
      onSuccess: async (result) => {
        // Invalidate the step to update its metadata
        await utils.jobs.matches.steps.getByMatchAndType.invalidate();
        // Also invalidate the match to update its status
        await utils.jobs.matches.get.invalidate();
        // Invalidate offer history
        await utils.jobs.matches.timeline.getOfferHistory.invalidate();
      },
      onError: () => {
        toast.error(i18n.en.error.rate.finalize);
      },
    });

  const finalizeNegotiation = useCallback(
    async (data: { matchId: string; agreedRate: number; notes?: string }) => {
      return await finalizeNegotiationMutation.mutateAsync(data);
    },
    [finalizeNegotiationMutation],
  );

  return useMemo(
    () => ({
      finalizeNegotiation,
      isLoading: finalizeNegotiationMutation.isPending,
      error: finalizeNegotiationMutation.error,
    }),
    [
      finalizeNegotiation,
      finalizeNegotiationMutation.isPending,
      finalizeNegotiationMutation.error,
    ],
  );
}

/**
 * Timeline and history hooks
 */

/**
 * Hook for getting match timeline
 * @param matchId - The ID of the match
 * @param filters - Optional filters for the timeline
 * @returns Timeline data and loading state
 */
export function useMatchTimeline(
  matchId?: string,
  filters?: {
    startDate?: Date;
    endDate?: Date;
    eventTypes?: TimelineEventType[];
    actorTypes?: TimelineActorType[];
    pageSize?: number;
    pageNumber?: number;
  },
) {
  const {
    data: timeline,
    isLoading,
    error,
  } = api.jobs.matches.timeline.getMatchTimeline.useQuery(
    {
      matchId: matchId ?? "",
      filters: filters ?? {},
    },
    { enabled: !!matchId },
  );

  return useMemo(
    () => ({
      timeline,
      isLoading,
      error,
    }),
    [timeline, isLoading, error],
  );
}

/**
 * Hook for getting match history
 * @param matchId - The ID of the match
 * @param filters - Optional filters for the history
 * @returns History data and loading state
 */
export function useMatchHistory(
  matchId?: string,
  filters?: {
    startDate?: Date;
    endDate?: Date;
    eventTypes?: TimelineEventType[];
    actorTypes?: TimelineActorType[];
    pageSize?: number;
    pageNumber?: number;
  },
) {
  const {
    data: history,
    isLoading,
    error,
  } = api.jobs.matches.timeline.getMatchHistory.useQuery(
    {
      matchId: matchId ?? "",
      filters: filters ?? {},
    },
    { enabled: !!matchId },
  );

  return useMemo(
    () => ({
      history,
      isLoading,
      error,
    }),
    [history, isLoading, error],
  );
}

/**
 * Hook for getting offer history
 * @param matchId - The ID of the match
 * @param filters - Optional filters for the offer history
 * @returns Offer history data and loading state
 */
export function useOfferHistory(
  matchId?: string,
  filters?: {
    startDate?: Date;
    endDate?: Date;
    actorTypes?: TimelineActorType[];
    pageSize?: number;
    pageNumber?: number;
  },
) {
  const {
    data: offerHistory,
    isLoading,
    error,
  } = api.jobs.matches.timeline.getOfferHistory.useQuery(
    {
      matchId: matchId ?? "",
      filters: filters ?? {},
    },
    { enabled: !!matchId },
  );

  return useMemo(
    () => ({
      offerHistory,
      isLoading,
      error,
    }),
    [offerHistory, isLoading, error],
  );
}

/**
 * Admin dashboard hooks
 */

/**
 * Hook for getting all matches with pagination
 * @param filters - Optional filters for the matches
 * @returns Matches data and loading state
 */
export function useAdminMatches(filters?: {
  status?: string;
  organizationId?: string;
  providerId?: string;
  jobId?: string;
  pageSize?: number;
  pageNumber?: number;
}) {
  const {
    data: matches,
    isLoading,
    error,
  } = api.admin.matches.getAll.useQuery(
    {
      filters: filters ?? {},
    },
    { enabled: !!filters },
  );

  return useMemo(
    () => ({
      matches,
      isLoading,
      error,
    }),
    [matches, isLoading, error],
  );
}

/**
 * Hook for admin match operations
 * @returns Functions for admin match operations and loading state
 */
export function useAdminMatchOperations() {
  const utils = api.useUtils();

  const updateMatchMutation = api.admin.matches.update.useMutation({
    onSuccess: async (updatedMatch) => {
      await utils.admin.matches.getAll.invalidate();
      await utils.jobs.matches.get.invalidate({ id: updatedMatch.id });
    },
    onError: () => {
      toast.error(i18n.en.error.match.update);
    },
  });

  const updateMatch = useCallback(
    async (id: string, data: { status?: string; [key: string]: any }) => {
      return await updateMatchMutation.mutateAsync({ id, data });
    },
    [updateMatchMutation],
  );

  return useMemo(
    () => ({
      updateMatch,
      isLoading: updateMatchMutation.isPending,
      error: updateMatchMutation.error,
    }),
    [updateMatch, updateMatchMutation.isPending, updateMatchMutation.error],
  );
}

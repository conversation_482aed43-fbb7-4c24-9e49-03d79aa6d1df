import { TRPCError } from "@trpc/server";
import { z } from "zod";

import type { MatchStatus, Prisma } from "@axa/database-medical";

import { authorizedProcedure, createTRPCRouter } from "../../../trpc";

// Use Prisma-generated types as source of truth
type ActionWithActor = Prisma.ActionGetPayload<{
  include: { actor: true };
}>;

type MessageWithAuthor = Prisma.MessageGetPayload<{
  include: { author: true };
}>;

interface RateActionMetadata {
  offerType?: "INITIAL" | "COUNTER" | "ACCEPT" | "DECLINE";
  proposedRate?: number;
  acceptedRate?: number;
  previousRate?: number;
  rate?: number;
  message?: string;
  lastOfferBy?: "PROVIDER" | "ORGANIZATION";
  offerExpiresAt?: string;
  currentRound?: number;
  declineReason?: string;
}

export interface TimelineBlock {
  id: string;
  type:
    | "rate-offer"
    | "rate-accepted"
    | "rate-declined"
    | "rate-counter"
    | "message";
  timestamp: Date;
  actor: {
    id: string;
    name: string;
    type: "PROVIDER" | "ORGANIZATION" | "SYSTEM";
    avatar?: string;
  };
  data: Record<string, any>;
}

export interface TimelineResponse {
  // Core match data
  match: {
    id: string;
    status: MatchStatus;
    createdAt: Date;
    initiator: string;
  };

  // Timeline events
  blocks: TimelineBlock[];

  // Context data needed for UI
  job: {
    id: string;
    title: string;
    role: string;
    summary: string;
    paymentRate: number;
    allowRateNegotiation: boolean;
    minNegotiableRate?: number;
    maxNegotiableRate?: number;
  };

  provider: {
    id: string;
    name: string;
    title?: string;
    avatar?: string;
  };

  organization: {
    id: string;
    name: string;
    avatar?: string;
  };

  compensation?: {
    id: string;
    currentOfferRate?: number;
    finalAgreedRate?: number;
    lastOfferBy?: string;
    negotiationCount: number;
  };
}

// =====================================================
// TIMELINE ROUTER
// =====================================================

export const timelineRouter = createTRPCRouter({
  get: authorizedProcedure
    .input(
      z.object({
        matchId: z.string(),
      }),
    )
    .query(async ({ ctx, input }): Promise<TimelineResponse> => {
      // Fetch all match data in one query
      const match = await ctx.prisma.match.findUnique({
        where: { id: input.matchId },
        include: {
          actions: {
            include: {
              actor: true,
            },
            orderBy: {
              createdAt: "asc",
            },
          },
          thread: {
            include: {
              messages: {
                include: {
                  author: true,
                },
                orderBy: {
                  createdAt: "asc",
                },
              },
            },
          },
          provider: {
            include: {
              person: true,
            },
          },
          organization: true,
          job: true,
          compensation: true,
        },
      });

      if (!match) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Match not found",
        });
      }

      // Transform actions to timeline blocks
      const blocks: TimelineBlock[] = [];

      // Process rate negotiation actions
      for (const action of match.actions) {
        if (
          action.type.includes("RATE") ||
          (action.metadata as RateActionMetadata)?.proposedRate
        ) {
          const block = transformRateAction(action);
          if (block) {
            blocks.push(block);
          }
        }
      }

      // Process messages related to rate negotiation
      if (match.thread?.messages) {
        for (const message of match.thread.messages) {
          const block = transformMessage(message);
          blocks.push(block);
        }
      }

      // Sort all blocks by timestamp
      blocks.sort(
        (a, b) =>
          new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime(),
      );

      return {
        match: {
          id: match.id,
          status: match.status,
          createdAt: match.createdAt,
          initiator: match.initiator,
        },
        blocks,
        job: {
          id: match.job.id,
          title: match.job.role,
          role: match.job.role,
          summary: match.job.summary,
          paymentRate: match.job.paymentRate,
          allowRateNegotiation: match.job.allowRateNegotiation,
          minNegotiableRate: match.job.minNegotiableRate ?? undefined,
          maxNegotiableRate: match.job.maxNegotiableRate ?? undefined,
        },
        provider: {
          id: match.provider.id,
          name: `${match.provider.person.firstName} ${match.provider.person.lastName}`,
          title: match.provider.title ?? undefined,
          avatar: match.provider.person.avatar ?? undefined,
        },
        organization: {
          id: match.organization.id,
          name: match.organization.name,
          avatar: match.organization.avatar ?? undefined,
        },
        compensation: match.compensation
          ? {
              id: match.compensation.id,
              currentOfferRate:
                match.compensation.currentOfferRate ?? undefined,
              finalAgreedRate: match.compensation.finalAgreedRate ?? undefined,
              lastOfferBy: match.compensation.lastOfferBy ?? undefined,
              negotiationCount: match.compensation.negotiationCount,
            }
          : undefined,
      };
    }),
});

// =====================================================
// HELPER FUNCTIONS
// =====================================================

function transformRateAction(action: ActionWithActor): TimelineBlock | null {
  const metadata = (action.metadata as RateActionMetadata) || {};

  // Create actor info
  const actor = {
    id: action.actor.id,
    name: `${action.actor.firstName} ${action.actor.lastName}`,
    type:
      action.actor.role === "PROVIDER"
        ? ("PROVIDER" as const)
        : ("ORGANIZATION" as const),
    avatar: action.actor.avatar ?? undefined,
  };

  // Transform based on rate action type
  if (metadata.offerType === "INITIAL" || action.type.includes("OFFER")) {
    return {
      id: action.id,
      type: "rate-offer",
      timestamp: action.createdAt,
      actor,
      data: {
        rate: metadata.proposedRate || metadata.rate,
        formattedRate: `$${metadata.proposedRate || metadata.rate}/hr`,
        message: metadata.message,
        offerBy: metadata.lastOfferBy || actor.type,
        expiresAt: metadata.offerExpiresAt,
      },
    };
  }

  if (metadata.offerType === "ACCEPT" || action.type.includes("ACCEPT")) {
    return {
      id: action.id,
      type: "rate-accepted",
      timestamp: action.createdAt,
      actor,
      data: {
        rate: metadata.acceptedRate || metadata.rate,
        formattedRate: `$${metadata.acceptedRate || metadata.rate}/hr`,
        message: metadata.message || "Rate offer accepted",
      },
    };
  }

  if (metadata.offerType === "DECLINE" || action.type.includes("DECLINE")) {
    return {
      id: action.id,
      type: "rate-declined",
      timestamp: action.createdAt,
      actor,
      data: {
        rate: metadata.proposedRate || metadata.rate,
        formattedRate: `$${metadata.proposedRate || metadata.rate}/hr`,
        message: metadata.message || "Rate offer declined",
        reason: metadata.declineReason,
      },
    };
  }

  if (metadata.offerType === "COUNTER" || action.type.includes("COUNTER")) {
    return {
      id: action.id,
      type: "rate-counter",
      timestamp: action.createdAt,
      actor,
      data: {
        previousRate: metadata.previousRate,
        proposedRate: metadata.proposedRate || metadata.rate,
        formattedRate: `$${metadata.proposedRate || metadata.rate}/hr`,
        message: metadata.message,
        round: metadata.currentRound || 1,
      },
    };
  }

  return null;
}

function transformMessage(message: MessageWithAuthor): TimelineBlock {
  return {
    id: message.id,
    type: "message",
    timestamp: message.createdAt,
    actor: {
      id: message.author.id,
      name: `${message.author.firstName} ${message.author.lastName}`,
      type: message.author.role === "PROVIDER" ? "PROVIDER" : "ORGANIZATION",
      avatar: message.author.avatar ?? undefined,
    },
    data: {
      content: message.content,
    },
  };
}
